<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="theme-color" content="#1f1f1f" />

    <!-- DNS Prefetch & Preconnect for Performance -->
    @foreach(config('seo.technical.dns_prefetch', []) as $domain)
        <link rel="dns-prefetch" href="{{ $domain }}" />
    @endforeach

    @foreach(config('seo.technical.preconnect', []) as $domain)
        <link rel="preconnect" href="{{ $domain }}" crossorigin />
    @endforeach

    <!-- Preload Critical Fonts -->
    @foreach(config('seo.technical.preload_fonts', []) as $font)
        <link rel="preload" href="{{ $font }}" as="style" onload="this.onload=null;this.rel='stylesheet'" />
        <noscript>
            <link rel="stylesheet" href="{{ $font }}">
        </noscript>
    @endforeach

    <title inertia>{{ config('app.name', 'Panda Patronage') }}</title>

    <!-- Favicons and App Icons -->
    <link rel="icon" type="image/x-icon" href="/images/misc/logo.png" />
    <link rel="apple-touch-icon" href="/images/misc/logo.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />

    @routes
    @viteReactRefresh
    @vite('resources/js/App.jsx')
    @inertiaHead
</head>

<body>
    @inertia
</body>

</html>