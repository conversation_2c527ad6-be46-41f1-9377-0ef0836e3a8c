import { Link } from "@inertiajs/react";
import BlogDetailSection from "../sections/BlogDetail/BlogDetailSection";
import BlogGridSection from "../sections/Blogs/BlogGridSection";
import { route } from "ziggy-js";

export default function BlogDetailDynamic({ article, latestArticles = [] }) {
  if (!article) {
    return (
      <div className="flex min-h-[60vh] flex-col items-center justify-center">
        <h2 className="mb-4 text-2xl font-bold">Blog not found</h2>
        <Link href={route('blog')} className="text-blue-600 underline">
          Back to all articles
        </Link>
      </div>
    );
  }

  return (
    <>
      <BlogDetailSection article={article} />
      <BlogGridSection
        articles={latestArticles}
        title="Latest Articles"
        subtitle="Stay informed with the latest guides and news."
        className="mt-20 bg-white"
      />
    </>
  );
}
