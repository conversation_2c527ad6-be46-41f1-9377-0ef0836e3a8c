<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="theme-color" content="#1f1f1f" />

    <!-- DNS Prefetch & Preconnect for Performance -->
    <?php $__currentLoopData = config('seo.technical.dns_prefetch', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $domain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <link rel="dns-prefetch" href="<?php echo e($domain); ?>" />
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <?php $__currentLoopData = config('seo.technical.preconnect', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $domain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <link rel="preconnect" href="<?php echo e($domain); ?>" crossorigin />
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <!-- Preload Critical Fonts -->
    <?php $__currentLoopData = config('seo.technical.preload_fonts', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $font): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <link rel="preload" href="<?php echo e($font); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'" />
        <noscript>
            <link rel="stylesheet" href="<?php echo e($font); ?>">
        </noscript>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <title inertia><?php echo e(config('app.name', 'Panda Patronage')); ?></title>

    <!-- Favicons and App Icons -->
    <link rel="icon" type="image/x-icon" href="/images/misc/logo.png" />
    <link rel="apple-touch-icon" href="/images/misc/logo.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />

    <?php echo app('Tighten\Ziggy\BladeRouteGenerator')->generate(); ?>
    <?php echo app('Illuminate\Foundation\Vite')->reactRefresh(); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/js/App.jsx'); ?>
    <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->head; } ?>
</head>

<body>
    <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->body; } else { ?><div id="app" data-page="<?php echo e(json_encode($page)); ?>"></div><?php } ?>
</body>

</html><?php /**PATH C:\laragon\www\panda-patronage\resources\views/app.blade.php ENDPATH**/ ?>