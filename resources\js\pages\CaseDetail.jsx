import React from "react";
import ProjectShowcase from "../components/ProjectShowcase";
import SeoHead from "../components/SeoHead";

const CaseDetail = ({ caseDetail = caseDetail, cases = [], seoData }) => {
    return (
        <React.Fragment>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <div className="mx-auto flex min-h-screen max-w-5xl w-full flex-col items-center bg-white">
                {/* Header using flexbox, not absolute positioning */}
                <div className="flex items-start self-start justify-start space-x-4 pb-6">
                    {/* <!-- Logo circle --> */}
                    <div className="flex size-10 items-center justify-center shadow-lg bg-transparent rounded-[50%]">
                        <img
                            src={caseDetail.logo}
                            alt={caseDetail.companyName}
                            className="size-10 rounded-[50%] object-contain"
                        />
                    </div>

                    {/* <!-- Text block --> */}
                    <div>
                        <h1 className="text-base font-bold text-gray-900">
                            {caseDetail.projectName}
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            {caseDetail.companyName}
                        </p>
                    </div>
                </div>
                {/* Image Stack */}
                <div className="mt-0 flex w-full flex-col gap-0 items-center">
                    {caseDetail.images.map((img, idx) => (
                        <img
                            key={idx}
                            src={img}
                            alt={`Case image ${idx + 1}`}
                            className="m-0 aspect-[16/9] w-full max-w-6xl object-contain p-0"
                            style={{ display: "block" }}
                        />
                    ))}
                </div>
                {/* More Cases Section */}
                <div className="mx-auto mt-12 w-full max-w-6xl">
                    <h2 className="mb-6 text-2xl font-bold text-[#2d2318]">
                        More cases
                    </h2>
                    <ProjectShowcase cases={cases} />
                </div>
            </div>
        </React.Fragment>
    );
};

export default CaseDetail;
