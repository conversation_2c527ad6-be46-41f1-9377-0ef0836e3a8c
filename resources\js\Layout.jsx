import DesktopHeader from "./components/DesktopHeader";
import MobileHeader from "./components/MobileHeader";
import Footer from "./components/Footer";
import SkipLink from "./components/SkipLink";
import { usePage } from "@inertiajs/react";
import { useEffect } from "react";
import { route } from "ziggy-js";

const navLinks = [
    { label: "Home", href: route("home") },
    { label: "Cases", href: route("cases") },
    { label: "About", href: route("about") },
    { label: "Blog", href: route("blog") },
    { label: "Contact", href: route("contact") },
];

const Layout = ({ children }) => {
    const { url } = usePage();

    const activeLink = navLinks.find((link) =>
        link.href === route("home")
            ? url === route("home")
            : url.startsWith(link.href),
    )?.label;

    const isHome = url === route("home");

    useEffect(() => {
        window.scrollTo({
            top: 0,
            left: 0,
            behavior: "smooth",
        });
    }, [url]);

    return (
        <div
            className={`relative ${isHome ? "bg-[rgb(245,246,249)]" : "bg-white"}`}
        >
            <SkipLink />
            <DesktopHeader activeLink={activeLink} />
            <MobileHeader activeLink={activeLink} />
            <main id="main-content" role="main">
                {children}
            </main>
            <Footer />
        </div>
    );
};

export default Layout;
