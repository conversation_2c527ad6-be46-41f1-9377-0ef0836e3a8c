@import url("https://fonts.googleapis.com/css2?family=Great+Vibes&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import "tailwindcss";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --breakpoint-xs: 31.375rem; /* 502px */
    --breakpoint-tablet: 50.625rem; /* 810px */
    --breakpoint-desktop: 80rem; /* 1280px */
}

body {
    font-family: "Roboto", sans-serif;
}
@utility font-greatvibes {
    font-family: "Great Vibes", cursive;
}

/* Enhanced ScrollReveal Animations - Hardware Accelerated */
@layer utilities {
    /* Base animation utilities with hardware acceleration */
    .scroll-reveal-base {
        will-change: transform, opacity;
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
    }

    /* Performance optimized transforms */
    .gpu-accelerated {
        transform: translateZ(0);
        will-change: transform, opacity;
    }

    /* Smooth animation curves */
    .ease-reveal {
        transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .ease-reveal-fast {
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }

    .ease-reveal-slow {
        transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    /* Disable ScrollReveal animations for users who prefer reduced motion */
    .scroll-reveal-base {
        will-change: auto;
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .scroll-reveal-base {
        /* Ensure animations don't interfere with high contrast */
        filter: none;
    }
}

/* Performance optimizations for lower-end devices */
@media (max-width: 768px) {
    .scroll-reveal-base {
        /* Reduce animation complexity on mobile */
        will-change: opacity;
    }
}

/* Dark mode considerations */
@media (prefers-color-scheme: dark) {
    .scroll-reveal-base {
        /* Ensure animations work well in dark mode */
        color-scheme: dark;
    }
}
@keyframes logo-slider {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}
.animate-logo-slider {
    animation: logo-slider 18s linear infinite;
    width: max-content;
    display: flex;
}
.animate-logo-slider:hover {
    animation-play-state: paused;
}
