<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug',
        'image',
        'date',
        'tag',
        'title',
        'description',
        'content',
        'writer',
        'read_time',
    ];

    protected $casts = [
        'content' => 'array',
        'writer' => 'array',
    ];
}