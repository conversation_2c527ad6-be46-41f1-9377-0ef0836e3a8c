import CasesIntro from "../sections/Cases/CasesIntro";
import ProjectShowcase from "../components/ProjectShowcase";
import FaqsSection from "../components/FaqsSection";
import SeoHead from "../components/SeoHead";

const cases = [
    {
        title: "Codify",
        img: "/images/projects/codify.png",
        tags: ["Agency", "Portfolio", "Saas"],
        slug: "codify",
        url: "/cases/codify",
    },
    {
        title: "Taskify",
        img: "/images/projects/taskify.png",
        tags: ["Business", "AI", "Saas"],
        slug: "taskify",
        url: "/cases/taskify",
    },
    {
        title: "Flexify",
        img: "/images/projects/flexify.png",
        tags: ["Saas", "AI", "Business"],
        slug: "flexify",
        url: "/cases/flexify",
    },
    {
        title: "Landify",
        img: "/images/projects/landify.png",
        tags: ["Business", "Portfolio", "Landing"],
        slug: "landify",
        url: "/cases/landify",
    },
    {
        title: "Nexus AI",
        img: "/images/projects/nexus-ai.png",
        tags: ["AI", "Saas", "Business"],
        slug: "nexus-ai",
        url: "/cases/nexus-ai",
    },
    {
        title: "Todofusion",
        img: "/images/projects/todofusion.png",
        tags: ["AI", "Business", "Agency"],
        slug: "todofusion",
        url: "/cases/todofusion",
    },
    {
        title: "Brandify",
        img: "/images/projects/taskify.png",
        tags: ["Branding", "Portfolio", "Saas"],
        slug: "brandify",
        url: "/cases/brandify",
    },
    {
        title: "MarketPro",
        img: "/images/projects/codify.png",
        tags: ["Marketing", "Business", "AI"],
        slug: "marketpro",
        url: "/cases/marketpro",
    },
    {
        title: "Insightly",
        img: "/images/projects/flexify.png",
        tags: ["Analytics", "Saas", "Business"],
        slug: "insightly",
        url: "/cases/insightly",
    },
    {
        title: "Creatify",
        img: "/images/projects/landify.png",
        tags: ["Creative", "Portfolio", "Agency"],
        slug: "creatify",
        url: "/cases/creatify",
    },
    {
        title: "Optima",
        img: "/images/projects/nexus-ai.png",
        tags: ["Optimization", "AI", "Saas"],
        slug: "optima",
        url: "/cases/optima",
    },
    {
        title: "Visionary",
        img: "/images/projects/todofusion.png",
        tags: ["Vision", "Business", "Portfolio"],
        slug: "visionary",
        url: "/cases/visionary",
    },
];

const Cases = ({ seoData }) => {
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <CasesIntro />
            <ProjectShowcase cases={cases} />
            <FaqsSection />
        </>
    );
};

export default Cases;
