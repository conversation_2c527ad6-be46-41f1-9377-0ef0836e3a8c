import CasesIntro from "../sections/Cases/CasesIntro";
import ProjectShowcase from "../components/ProjectShowcase";
import FaqsSection from "../components/FaqsSection";
import SeoHead from "../components/SeoHead";

const cases = [
    {
        title: "Codify",
        img: "/images/projects/codify.png",
        tags: ["Agency", "Portfolio", "Saas"],
        url: "#",
    },
    {
        title: "Taskify",
        img: "/images/projects/taskify.png",
        tags: ["Business", "AI", "Saas"],
        url: "#",
    },
    {
        title: "Flexify",
        img: "/images/projects/flexify.png",
        tags: ["Saas", "AI", "Business"],
        url: "#",
    },
    {
        title: "Landify",
        img: "/images/projects/landify.png",
        tags: ["Business", "Portfolio", "Landing"],
        url: "#",
    },
    {
        title: "Nexus AI",
        img: "/images/projects/nexus-ai.png",
        tags: ["AI", "Saas", "Business"],
        url: "#",
    },
    {
        title: "Todofusion",
        img: "/images/projects/todofusion.png",
        tags: ["AI", "Business", "Agency"],
        url: "#",
    },
    {
        title: "Brandify",
        img: "/images/projects/taskify.png",
        tags: ["Branding", "Portfolio", "Saas"],
        url: "#",
    },
    {
        title: "MarketPro",
        img: "/images/projects/codify.png",
        tags: ["Marketing", "Business", "AI"],
        url: "#",
    },
    {
        title: "Insightly",
        img: "/images/projects/flexify.png",
        tags: ["Analytics", "Saas", "Business"],
        url: "#",
    },
    {
        title: "Creatify",
        img: "/images/projects/landify.png",
        tags: ["Creative", "Portfolio", "Agency"],
        url: "#",
    },
    {
        title: "Optima",
        img: "/images/projects/nexus-ai.png",
        tags: ["Optimization", "AI", "Saas"],
        url: "#",
    },
    {
        title: "Visionary",
        img: "/images/projects/todofusion.png",
        tags: ["Vision", "Business", "Portfolio"],
        url: "#",
    },
];

const Cases = ({ seoData }) => {
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <CasesIntro />
            <ProjectShowcase cases={cases} />
            <FaqsSection />
        </>
    );
};

export default Cases;
