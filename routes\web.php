<?php

use Illuminate\Support\Facades\Route;
use App\Models\Article;

Route::get('/', function () {
    return inertia('Home');
})->name('home');

Route::get('/cases', function () {
    return inertia('Cases');
})->name('cases');

Route::get('/about', function () {
    return inertia('About');
})->name('about');

Route::get('/blog', function () {
    $articles = Article::all();
    return inertia('Blogs', ['articles' => $articles]);
})->name('blog');

Route::get('/contact', function () {
    return inertia('Contact');
})->name('contact');

Route::get('/privacy-policy', function () {
    return inertia('PrivacyPolicy');
})->name('privacy-policy');

Route::get('/licensing', function () {
    return inertia('Licensing');
})->name('licensing');

Route::get('/terms-of-use', function () {
    return inertia('TermsOfUse');
})->name('terms-of-use');

Route::get('/blog/{slug}', function ($slug) {
    $article = Article::where('slug', $slug)->first();
    
    if (!$article) {
        abort(404);
    }
    
    return inertia('BlogDetailDynamic', ['article' => $article]);
})->name('blog.detail');


