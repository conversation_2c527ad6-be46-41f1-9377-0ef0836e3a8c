<?php

use Illuminate\Support\Facades\Route;
use App\Models\Article;

Route::get('/', function () {
    $seoData = seo_page_data([
        'title' => 'Panda Patronage - Digital Marketing & Web Development Agency',
        'description' => 'Transform your digital presence with Panda Patronage. Expert web development, digital marketing, and creative solutions to grow your business online.',
        'keywords' => 'web development, digital marketing, Laravel, React, SEO, social media marketing, web design, Montreal agency',
        'og:image' => url('/images/misc/og-home.jpg'),
        'og:type' => 'website',
    ], [
        'organization' => seo_structured_data('Organization'),
        'website' => seo_structured_data('WebSite'),
    ]);

    return inertia('Home', compact('seoData'));
})->name('home');

Route::get('/cases', function () {
    $seoData = seo_page_data([
        'title' => 'Our Portfolio & Case Studies',
        'description' => 'Explore our portfolio of successful web development and digital marketing projects. See how we\'ve helped businesses grow their online presence.',
        'keywords' => 'portfolio, case studies, web development projects, digital marketing campaigns, client success stories',
        'og:image' => url('/images/misc/og-cases.jpg'),
        'og:type' => 'website',
    ], [
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Cases', 'url' => route('cases')],
        ]),
    ]);

    return inertia('Cases', compact('seoData'));
})->name('cases');

Route::get('/about', function () {
    $seoData = seo_page_data([
        'title' => 'About Us - Meet the Panda Patronage Team',
        'description' => 'Learn about Panda Patronage, our mission, values, and the talented team behind our digital marketing and web development services.',
        'keywords' => 'about us, team, company, digital agency, web development team, Montreal, mission, values',
        'og:image' => url('/images/misc/og-about.jpg'),
        'og:type' => 'website',
    ], [
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'About', 'url' => route('about')],
        ]),
    ]);

    return inertia('About', compact('seoData'));
})->name('about');

Route::get('/blog', function () {
    $articles = Article::all();

    $seoData = seo_page_data([
        'title' => 'Blog - Digital Marketing & Web Development Insights',
        'description' => 'Stay updated with the latest trends in web development, digital marketing, and technology. Expert insights and tips from Panda Patronage.',
        'keywords' => 'blog, digital marketing blog, web development articles, SEO tips, technology insights, tutorials',
        'og:image' => url('/images/misc/og-blog.jpg'),
        'og:type' => 'website',
    ], [
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Blog', 'url' => route('blog')],
        ]),
    ]);

    return inertia('Blogs', compact('articles', 'seoData'));
})->name('blog');

Route::get('/contact', function () {
    $seoData = seo_page_data([
        'title' => 'Contact Us - Get in Touch with Panda Patronage',
        'description' => 'Ready to transform your digital presence? Contact Panda Patronage for web development, digital marketing, and creative solutions. Located in Montreal, Canada.',
        'keywords' => 'contact, get in touch, Montreal web development, digital marketing agency, consultation, quote',
        'og:image' => url('/images/misc/og-contact.jpg'),
        'og:type' => 'website',
    ], [
        'organization' => seo_structured_data('Organization'),
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Contact', 'url' => route('contact')],
        ]),
    ]);

    return inertia('Contact', compact('seoData'));
})->name('contact');

Route::get('/privacy-policy', function () {
    return inertia('PrivacyPolicy');
})->name('privacy-policy');

Route::get('/licensing', function () {
    return inertia('Licensing');
})->name('licensing');

Route::get('/terms-of-use', function () {
    return inertia('TermsOfUse');
})->name('terms-of-use');

Route::get('/blog/{slug}', function ($slug) {
    $article = Article::where('slug', $slug)->first();

    if (!$article) {
        abort(404);
    }

    $seoData = seo_page_data([
        'title' => $article->title,
        'description' => seo_truncate($article->description, 160),
        'keywords' => $article->tag . ', blog, digital marketing, web development',
        'og:image' => url($article->image),
        'og:type' => 'article',
        'og:article:published_time' => $article->created_at->toISOString(),
        'og:article:modified_time' => $article->updated_at->toISOString(),
        'og:article:author' => 'Panda Patronage',
        'og:article:section' => $article->tag,
    ], [
        'article' => seo_structured_data('Article', [
            'headline' => $article->title,
            'description' => $article->description,
            'image' => url($article->image),
            'datePublished' => $article->created_at->toISOString(),
            'dateModified' => $article->updated_at->toISOString(),
            'articleSection' => $article->tag,
        ]),
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Blog', 'url' => route('blog')],
            ['name' => $article->title, 'url' => route('blog.detail', $article->slug)],
        ]),
    ]);

    return inertia('BlogDetailDynamic', compact('article', 'seoData'));
})->name('blog.detail');

// SEO Routes
Route::get('/sitemap.xml', function () {
    $sitemap = file_get_contents(public_path('sitemap.xml'));
    return response($sitemap, 200, [
        'Content-Type' => 'application/xml',
        'Cache-Control' => 'public, max-age=3600',
    ]);
})->name('sitemap');

Route::get('/robots.txt', function () {
    $robots = file_get_contents(public_path('robots.txt'));
    return response($robots, 200, [
        'Content-Type' => 'text/plain',
        'Cache-Control' => 'public, max-age=86400',
    ]);
})->name('robots');


