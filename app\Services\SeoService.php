<?php

namespace App\Services;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class SeoService
{
    protected array $defaultMeta = [
        'title' => 'Panda Patronage - Digital Marketing & Web Development Agency',
        'description' => 'Transform your digital presence with Panda Patronage. Expert web development, digital marketing, and creative solutions to grow your business online.',
        'keywords' => 'web development, digital marketing, Laravel, React, SEO, social media marketing, web design',
        'author' => 'Panda Patronage',
        'robots' => 'index, follow',
        'og:type' => 'website',
        'og:site_name' => 'Panda Patronage',
        'twitter:card' => 'summary_large_image',
        'twitter:site' => '@pandapatronage',
    ];

    protected array $companyInfo = [
        'name' => 'Panda Patronage',
        'url' => 'https://pandapatronage.com',
        'logo' => '/images/misc/logo.png',
        'address' => 'Montreal, Canada, 110 Notre-Dame St W',
        'phone' => '******-XXX-XXXX',
        'email' => '<EMAIL>',
        'social' => [
            'facebook' => 'https://facebook.com/pandapatronage',
            'twitter' => 'https://twitter.com/pandapatronage',
            'linkedin' => 'https://linkedin.com/company/pandapatronage',
            'instagram' => 'https://instagram.com/pandapatronage',
        ],
    ];

    public function generateMeta(array $customMeta = []): array
    {
        $meta = array_merge($this->defaultMeta, $customMeta);
        
        // Ensure title is properly formatted
        if (!str_contains($meta['title'], 'Panda Patronage') && $meta['title'] !== $this->defaultMeta['title']) {
            $meta['title'] .= ' | Panda Patronage';
        }

        // Generate Open Graph tags
        $meta['og:title'] = $meta['og:title'] ?? $meta['title'];
        $meta['og:description'] = $meta['og:description'] ?? $meta['description'];
        $meta['og:url'] = $meta['og:url'] ?? URL::current();
        $meta['og:image'] = $meta['og:image'] ?? URL::to('/images/misc/og-default.jpg');

        // Generate Twitter Card tags
        $meta['twitter:title'] = $meta['twitter:title'] ?? $meta['title'];
        $meta['twitter:description'] = $meta['twitter:description'] ?? $meta['description'];
        $meta['twitter:image'] = $meta['twitter:image'] ?? $meta['og:image'];

        // Generate canonical URL
        $meta['canonical'] = $meta['canonical'] ?? URL::current();

        return $meta;
    }

    public function generateStructuredData(string $type, array $data = []): array
    {
        $baseStructure = [
            '@context' => 'https://schema.org',
            '@type' => $type,
        ];

        switch ($type) {
            case 'Organization':
                return array_merge($baseStructure, [
                    'name' => $this->companyInfo['name'],
                    'url' => $this->companyInfo['url'],
                    'logo' => URL::to($this->companyInfo['logo']),
                    'address' => [
                        '@type' => 'PostalAddress',
                        'streetAddress' => $this->companyInfo['address'],
                        'addressCountry' => 'CA',
                    ],
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'telephone' => $this->companyInfo['phone'],
                        'contactType' => 'customer service',
                        'email' => $this->companyInfo['email'],
                    ],
                    'sameAs' => array_values($this->companyInfo['social']),
                ], $data);

            case 'WebSite':
                return array_merge($baseStructure, [
                    'name' => $this->companyInfo['name'],
                    'url' => $this->companyInfo['url'],
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => $this->companyInfo['url'] . '/search?q={search_term_string}',
                        'query-input' => 'required name=search_term_string',
                    ],
                ], $data);

            case 'Article':
                return array_merge($baseStructure, [
                    'headline' => $data['title'] ?? '',
                    'description' => $data['description'] ?? '',
                    'image' => URL::to($data['image'] ?? '/images/misc/og-default.jpg'),
                    'author' => [
                        '@type' => 'Organization',
                        'name' => $this->companyInfo['name'],
                    ],
                    'publisher' => [
                        '@type' => 'Organization',
                        'name' => $this->companyInfo['name'],
                        'logo' => [
                            '@type' => 'ImageObject',
                            'url' => URL::to($this->companyInfo['logo']),
                        ],
                    ],
                    'datePublished' => $data['datePublished'] ?? now()->toISOString(),
                    'dateModified' => $data['dateModified'] ?? now()->toISOString(),
                ], $data);

            case 'Service':
                return array_merge($baseStructure, [
                    'name' => $data['name'] ?? '',
                    'description' => $data['description'] ?? '',
                    'provider' => [
                        '@type' => 'Organization',
                        'name' => $this->companyInfo['name'],
                    ],
                    'serviceType' => $data['serviceType'] ?? 'Digital Marketing',
                ], $data);

            default:
                return array_merge($baseStructure, $data);
        }
    }

    public function generateBreadcrumbs(array $breadcrumbs): array
    {
        $items = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url'] ?? null,
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items,
        ];
    }

    public function truncateDescription(string $description, int $maxLength = 160): string
    {
        if (strlen($description) <= $maxLength) {
            return $description;
        }

        return Str::limit($description, $maxLength - 3, '...');
    }

    public function getCompanyInfo(): array
    {
        return $this->companyInfo;
    }
}
