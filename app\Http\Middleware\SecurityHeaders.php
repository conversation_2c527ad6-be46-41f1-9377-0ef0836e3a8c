<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Set security headers
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

        // Content Security Policy (adjust for development/production)
        $scriptSrc = "'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com";
        $connectSrc = "'self'";

        // Allow Vite dev server in development
        if (app()->environment('local')) {
            $scriptSrc .= " http://localhost:5173 http://[::1]:5173";
            $connectSrc .= " ws://localhost:5173 ws://[::1]:5173 http://localhost:5173 http://[::1]:5173";
        }

        $csp = "default-src 'self'; " .
            "script-src {$scriptSrc}; " .
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com; " .
            "font-src 'self' https://fonts.gstatic.com; " .
            "img-src 'self' data: https:; " .
            "connect-src {$connectSrc};";

        $response->headers->set('Content-Security-Policy', $csp);

        return $response;
    }
}
